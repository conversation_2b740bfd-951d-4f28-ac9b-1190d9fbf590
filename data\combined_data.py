# climate和weather是一个意思
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
from typing import List, Tuple

class CombinedDataset(Dataset):
    def __init__(
        self,
        base_dir: str,
        years: List[int],
        num_bins: int = 64,
        months: Tuple[int, int] = (3, 10),
        histogram_norm: str = 'std',
        weather_norm: str = 'minmax',
        return_county_year: bool = False,
        weather_augment: bool = False,
        weather_noise_scale: float = 0.05,
        histogram_augment: bool = False,
        histogram_noise_scale: float = 0.05
    ):
        """
        组合数据集类，同时加载直方图数据、气候数据和产量数据
        
        Args:
            base_dir: 数据根目录
            years: 年份列表，如[2017, 2018, ...]
            num_bins: 直方图的bin数量
            months: 月份范围，默认(3, 10)表示3月到10月
            histogram_norm: 直方图归一化方式，可选值为 None, 'prob', 'minmax', 'std'
                           'prob': 对直方图进行概率归一化（各bin之和为1）
                           'minmax': 对直方图进行 min-max 归一化（缩放到[0,1]区间）
                           'std': 对直方图进行标准化（均值为0，标准差为1）
                           None: 不进行归一化
            weather_norm: 气候数据归一化方式，可选值为 None, 'minmax', 'std'
                         'minmax': 对气候数据进行 min-max 归一化（缩放到[0,1]区间）
                         'std': 对气候数据进行标准化（均值为0，标准差为1）
                         None: 不进行归一化
            return_county_year: 是否返回县和年份信息，用于对比学习
            weather_augment: 是否对气候数据进行噪声增强
            weather_noise_scale: 气候数据噪声增强的标准差大小
            histogram_augment: 是否对直方图数据进行噪声增强
            histogram_noise_scale: 直方图数据噪声增强的标准差大小
        """
        self.base_dir = Path(base_dir)
        self.years = sorted(years)
        self.num_bins = num_bins
        self.months = list(range(months[0], months[1] + 1))
        self.return_county_year = return_county_year
        
        # 验证归一化参数
        valid_histogram_norms = [None, 'prob', 'minmax', 'std']
        valid_weather_norms = [None, 'minmax', 'std']
        
        assert histogram_norm in valid_histogram_norms, f"histogram_norm 必须是 {valid_histogram_norms} 之一"
        assert weather_norm in valid_weather_norms, f"weather_norm 必须是 {valid_weather_norms} 之一"
        
        self.histogram_norm = histogram_norm
        self.weather_norm = weather_norm
        
        # 气候数据增强参数
        self.weather_augment = weather_augment
        self.weather_noise_scale = weather_noise_scale
        
        # 直方图数据增强参数
        self.histogram_augment = histogram_augment
        self.histogram_noise_scale = histogram_noise_scale
        
        # 存储所有有效的(year, fips)组合
        self.valid_samples = []
        # 存储加载的直方图数据
        self.histogram_cache = {}
        # 存储加载的气候数据
        self.climate_cache = {}
        # 存储加载的产量数据
        self.yield_cache = {}
        
        self._load_data()
    
    def _load_data(self):
        """加载并验证数据"""
        for year in self.years:
            # 加载直方图数据
            hist_path = self.base_dir / "histograms" / f"{year}_all_counties_histogram_{self.num_bins}.npz"
            if not hist_path.exists():
                print(f"警告：{year}年的直方图数据不可用，跳过...")
                continue
            
            # 加载气候数据
            climate_path = self.base_dir / "weather" / f"climate_data_{year}.csv"
            if not climate_path.exists():
                print(f"警告：{year}年的气候数据不可用，跳过...")
                continue
            
            # 加载产量数据
            yield_path = self.base_dir / "USDA" / "Soybean" / str(year) / f"USDA_Soybean_County_{year}.csv"
            if not yield_path.exists():
                print(f"警告：{year}年的产量数据不可用，跳过...")
                continue
            
            # 加载直方图数据
            hist_data = np.load(hist_path)
            hist_fips = hist_data['fipscodes']
            
            # 加载气候数据
            climate_df = pd.read_csv(climate_path)
            
            # 加载产量数据
            yield_df = pd.read_csv(yield_path)
            # 确保state_fips_code和county_fips_code都是两位数的字符串
            yield_df['state_fips_code'] = yield_df['state_ansi'].astype(str).str.zfill(2)
            yield_df['county_fips_code'] = yield_df['county_ansi'].astype(str).str.zfill(3)
            # 组合成完整的fips_code
            yield_df['fips_code'] = yield_df['state_fips_code'] + yield_df['county_fips_code']
            
            # 确保fips_code格式统一
            climate_df['fips_code'] = climate_df['fips_code'].astype(str).str.zfill(5)
            
            # 找到同时存在于所有数据集中的fips codes
            yield_fips = yield_df[~yield_df['YIELD, MEASURED IN BU / ACRE'].isna()]['fips_code'].unique()
            common_fips = list(set(hist_fips) & set(climate_df['fips_code'].unique()) & set(yield_fips))
            
            if not common_fips:
                print(f"警告：{year}年没有共同的FIPS codes，跳过...")
                continue
            
            # 存储有效的样本
            for fips in common_fips:
                self.valid_samples.append((year, fips))
            
            # 缓存数据
            self.histogram_cache[year] = {
                'data': hist_data['histograms'],
                'fips_map': {fips: idx for idx, fips in enumerate(hist_data['fipscodes'])}
            }
            
            # 处理并缓存气候数据
            year_climate = {}
            for fips in common_fips:
                fips_climate = climate_df[climate_df['fips_code'] == fips]
                # 选择指定月份的数据并重塑
                month_data = np.zeros((len(self.months), 6))  # 预分配数组，6个气候特征
                valid_data = True
                
                for i, month in enumerate(self.months):
                    month_climate = fips_climate[fips_climate['month'] == month]
                    if len(month_climate) == 0:
                        print(f"警告：{year}年{fips} {month}月的气候数据缺失")
                        valid_data = False
                        break
                    # 假设有6个气候特征
                    month_data[i] = month_climate.iloc[0, -6:].values
                
                if valid_data:
                    year_climate[fips] = month_data
            
            self.climate_cache[year] = year_climate
            
            # 缓存产量数据
            year_yield = {}
            for fips in common_fips:
                yield_value = yield_df[yield_df['fips_code'] == fips]['YIELD, MEASURED IN BU / ACRE'].values[0]
                year_yield[fips] = yield_value
            
            self.yield_cache[year] = year_yield
    
    def normalize_histogram(self, histogram: torch.Tensor, epsilon: float = 1e-8) -> torch.Tensor:
        """
        对直方图数据进行归一化处理
        
        Args:
            histogram: 形状为 (20, 6, num_bins) 的直方图数据
            epsilon: 防止除零的小数值
            
        Returns:
            归一化后的直方图数据
        """
        if self.histogram_norm == 'prob':
            # 在最后一维（bin维度）上求和
            bin_sums = histogram.sum(dim=-1, keepdim=True)
            # 归一化
            normalized = histogram / (bin_sums + epsilon)
        elif self.histogram_norm == 'minmax':
            # 在最后一维上进行min-max归一化
            min_vals = histogram.min(dim=-1, keepdim=True)[0]
            max_vals = histogram.max(dim=-1, keepdim=True)[0]
            # 防止除零
            denominator = (max_vals - min_vals)
            denominator[denominator == 0] = 1.0
            # 归一化到[0, 1]区间
            normalized = (histogram - min_vals) / denominator
        elif self.histogram_norm == 'std':
            # 标准化：将每个特征转换为均值为0，标准差为1的分布
            mean = histogram.mean(dim=-1, keepdim=True)
            std = histogram.std(dim=-1, keepdim=True)
            # 防止除零
            normalized = (histogram - mean) / (std + epsilon)
        else:
            normalized = histogram
            
        return normalized
    
    def normalize_climate(self, climate: torch.Tensor, epsilon: float = 1e-8) -> torch.Tensor:
        """
        对气候数据进行归一化处理
        
        Args:
            climate: 形状为 (num_months, 6) 的气候数据
            epsilon: 防止除零的小数值
            
        Returns:
            归一化后的气候数据
        """
        if self.weather_norm == 'minmax':
            # 计算每个特征的最小值和最大值
            min_vals = climate.min(dim=0, keepdim=True)[0]
            max_vals = climate.max(dim=0, keepdim=True)[0]
            # 防止除零
            denominator = (max_vals - min_vals)
            denominator[denominator == 0] = 1.0
            # 归一化到[0, 1]区间
            normalized = (climate - min_vals) / denominator
        elif self.weather_norm == 'std':
            # 标准化：将每个特征转换为均值为0，标准差为1的分布
            mean = climate.mean(dim=0, keepdim=True)
            std = climate.std(dim=0, keepdim=True)
            # 防止除零
            normalized = (climate - mean) / (std + epsilon)
        else:
            normalized = climate
            
        return normalized
    
    def __len__(self) -> int:
        return len(self.valid_samples)
    
    def __getitem__(self, idx: int) -> Tuple:
        year, fips = self.valid_samples[idx]
        
        # 获取直方图数据
        hist_data = self.histogram_cache[year]['data']
        hist_idx = self.histogram_cache[year]['fips_map'][fips]
        histogram = hist_data[hist_idx]  # shape: (20, 6, num_bins)
        
        # 获取气候数据
        climate = self.climate_cache[year][fips]  # shape: (8, 6)
        
        # 获取产量数据
        yield_value = self.yield_cache[year][fips]  # shape: (1,)
        
        # 转换为torch张量
        histogram = torch.FloatTensor(histogram)
        climate = torch.FloatTensor(climate)
        yield_value = torch.FloatTensor([yield_value])
        
        # 应用归一化
        if self.histogram_norm:
            histogram = self.normalize_histogram(histogram)
        
        if self.weather_norm:
            climate = self.normalize_climate(climate)
        
        # 应用直方图数据噪声增强 - 在bin维度上增加噪声
        if self.histogram_augment:
            # 应用随机高斯噪声到bin维度
            noise = torch.randn_like(histogram) * self.histogram_noise_scale
            histogram = histogram + noise
        
        # 应用气候数据噪声增强
        if self.weather_augment:
            # 应用随机高斯噪声
            noise = torch.randn_like(climate) * self.weather_noise_scale
            climate = climate + noise
        
        # 创建县和年份的唯一标识符
        # 使用县和年份组合作为索引，相同县和年份的样本会有相同的索引
        if self.return_county_year:
            # 将县和年份转换为唯一的整数索引
            # 使用字典映射，确保相同的(year, fips)组合有相同的索引
            county_year_idx = self.valid_samples.index((year, fips))
            return histogram, climate, yield_value, torch.tensor(county_year_idx)
        
        return histogram, climate, yield_value

def get_data_loader(
    base_dir: str,
    years: List[int],
    batch_size: int = 32,
    num_bins: int = 64,
    months: Tuple[int, int] = (3, 10),
    shuffle: bool = True,
    num_workers: int = 0,
    histogram_norm: str = 'std',
    weather_norm: str = 'minmax',
    return_county_year: bool = False,
    weather_augment: bool = False,
    weather_noise_scale: float = 0.05,
    histogram_augment: bool = False,
    histogram_noise_scale: float = 0.05
) -> DataLoader:
    """
    创建数据加载器
    
    Args:
        base_dir: 数据根目录
        years: 年份列表
        batch_size: 批次大小
        num_bins: 直方图的bin数量
        months: 月份范围
        shuffle: 是否打乱数据
        num_workers: 数据加载的工作进程数
        histogram_norm: 直方图归一化方式，可选值为 None, 'prob', 'minmax', 'std'
        weather_norm: 气候数据归一化方式，可选值为 None, 'minmax', 'std'
        return_county_year: 是否返回县和年份信息，用于对比学习
        weather_augment: 是否对气候数据进行噪声增强
        weather_noise_scale: 气候数据噪声增强的标准差大小
        histogram_augment: 是否对直方图数据进行噪声增强
        histogram_noise_scale: 直方图数据噪声增强的标准差大小
    
    Returns:
        DataLoader: PyTorch数据加载器
    """
    dataset = CombinedDataset(
        base_dir=base_dir,
        years=years,
        num_bins=num_bins,
        months=months,
        histogram_norm=histogram_norm,
        weather_norm=weather_norm,
        return_county_year=return_county_year,
        weather_augment=weather_augment,
        weather_noise_scale=weather_noise_scale,
        histogram_augment=histogram_augment,
        histogram_noise_scale=histogram_noise_scale
    )
    
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers
    )

# 使用示例
if __name__ == "__main__":
    # 设置基础路径
    base_dir = "datasets"
    years = list(range(2017, 2023))
    
    # 创建数据集实例用于测试
    dataset = CombinedDataset(
        base_dir=base_dir,
        years=years,
        num_bins=64,
        return_county_year=True
    )
    
    print("\n=== 数据集统计信息 ===")
    
    # 1. 统计每年的数据量
    year_counts = {}
    for year, fips in dataset.valid_samples:
        year_counts[year] = year_counts.get(year, 0) + 1
    
    print("\n1. 每年的有效样本数量：")
    for year in sorted(year_counts.keys()):
        print(f"{year}年: {year_counts[year]}个样本")
    
    # 2. 统计每年的FIPS codes
    year_fips = {}
    for year, fips in dataset.valid_samples:
        if year not in year_fips:
            year_fips[year] = set()
        year_fips[year].add(fips)
    
    print("\n2. 每年的FIPS codes数量：")
    for year in sorted(year_fips.keys()):
        print(f"{year}年: {len(year_fips[year])}个FIPS codes")
    
    # 3. 分析年份间FIPS codes的差异
    print("\n3. FIPS codes年份间的差异分析：")
    all_years = sorted(year_fips.keys())
    for i in range(len(all_years)-1):
        year1 = all_years[i]
        year2 = all_years[i+1]
        
        # 计算两年之间的FIPS差异
        only_in_year1 = year_fips[year1] - year_fips[year2]
        only_in_year2 = year_fips[year2] - year_fips[year1]
        common_fips = year_fips[year1] & year_fips[year2]
        
        print(f"\n{year1}年与{year2}年的对比：")
        print(f"- 共同的FIPS codes: {len(common_fips)}个")
        print(f"- 仅在{year1}年出现: {len(only_in_year1)}个")
        print(f"- 仅在{year2}年出现: {len(only_in_year2)}个")
    
    # 原有的数据加载测试
    data_loader = DataLoader(dataset, batch_size=32, shuffle=True)
    
    print("\n=== 数据加载测试 ===")
    for batch_idx, batch_data in enumerate(data_loader):
        if dataset.return_county_year:
            histograms, climate, yield_values, county_year_idx = batch_data
            print(f"Batch {batch_idx + 1}")
            print(f"Histogram shape: {histograms.shape}")  # 应该是 (batch_size, 20, 6, 64)
            print(f"Climate shape: {climate.shape}")       # 应该是 (batch_size, 8, 6)
            print(f"Yield shape: {yield_values.shape}")    # 应该是 (batch_size, 1)
            print(f"County Year Index shape: {county_year_idx.shape}")  # 应该是 (batch_size,)
        else:
            histograms, climate, yield_values = batch_data
            print(f"Batch {batch_idx + 1}")
            print(f"Histogram shape: {histograms.shape}")  # 应该是 (batch_size, 20, 6, 64)
            print(f"Climate shape: {climate.shape}")       # 应该是 (batch_size, 8, 6)
            print(f"Yield shape: {yield_values.shape}")    # 应该是 (batch_size, 1)
        break  # 只测试一个批次
