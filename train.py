import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from sklearn.metrics import r2_score
import matplotlib.pyplot as plt
import argparse
import yaml
from tqdm import tqdm
import random
from data.combined_data import get_data_loader
from model import MoEModel
import torch.nn.functional as F
import seaborn as sns
import pandas as pd
import pickle

def set_seed(seed):
    """设置随机种子以确保结果可重现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def pretrain_encoders(config):
    """使用对比学习预训练直方图编码器和气候编码器"""
    # 设置随机种子
    set_seed(config['seed'])
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config['use_gpu'] else 'cpu')
    print(f"Using device: {device}")
    
    # 加载数据
    print("Loading data...")
    
    # 使用预训练专用的数据集划分
    train_years = config['pretrain_train_years']  # 直接使用配置的训练年份
    val_years = config['pretrain_val_years']      # 直接使用配置的验证年份
    
    print(f"Pretraining years: {train_years}")
    print(f"Pretraining validation years: {val_years}")
    
    # 创建数据加载器
    train_loader = get_data_loader(
        base_dir=config['data_dir'],
        years=train_years,
        batch_size=config['batch_size'],
        num_bins=config['num_bins'],
        months=(config['start_month'], config['end_month']),
        shuffle=True,
        num_workers=config['num_workers'],
        histogram_norm=config['histogram_norm'],
        weather_norm=config['weather_norm'],
        return_county_year=True,  # 返回县和年份信息用于对比学习
        weather_augment=config.get('weather_augment', False),  # 获取气候数据增强配置
        weather_noise_scale=config.get('weather_noise_scale', 0.05),  # 获取气候噪声尺度配置
        histogram_augment=config.get('histogram_augment', False),  # 获取直方图数据增强配置
        histogram_noise_scale=config.get('histogram_noise_scale', 0.05)  # 获取直方图噪声尺度配置
    )
    
    val_loader = get_data_loader(
        base_dir=config['data_dir'],
        years=val_years,
        batch_size=config['batch_size'],
        num_bins=config['num_bins'],
        months=(config['start_month'], config['end_month']),
        shuffle=False,
        num_workers=config['num_workers'],
        histogram_norm=config['histogram_norm'],
        weather_norm=config['weather_norm'],
        return_county_year=True,  # 返回县和年份信息用于对比学习
        weather_augment=False,  # 验证集不使用数据增强
        weather_noise_scale=config.get('weather_noise_scale', 0.05),  # 获取气候噪声尺度配置
        histogram_augment=False,  # 验证集不使用直方图数据增强
        histogram_noise_scale=config.get('histogram_noise_scale', 0.05)  # 获取直方图噪声尺度配置
    )
    
    # 创建模型
    print("创建模型...")
    model = MoEModel(
        num_bins=config['num_bins'],
        num_months=config['end_month'] - config['start_month'] + 1,
        num_climate_features=6,  # 气候数据有6个特征
        hidden_dim=config['hidden_dim'],
        num_experts=config['num_experts'],
        dropout=config['dropout'],
        contrastive_weight=1.0,  # 预训练时对比损失权重为1.0
        temperature=config['temperature'],  # 温度参数
        pretrain_mode=True  # 设置为预训练模式
    ).to(device)
    
    # 计算并打印模型参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    
    # 定义优化器
    optimizer = optim.Adam(model.parameters(), lr=config['pretrain_lr'], weight_decay=config['weight_decay'])
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, 
        mode='min', 
        factor=config['lr_factor'], 
        patience=config['lr_patience']
    )
    
    # 训练循环
    print("开始预训练...")
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []
    
    for epoch in range(config['pretrain_epochs']):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for histograms, climate, yields, county_year_indices in tqdm(train_loader, desc=f"Epoch {epoch+1}/{config['pretrain_epochs']} [Train]"):
            histograms = histograms.to(device)
            climate = climate.to(device)
            county_year_indices = county_year_indices.to(device)
            
            # 前向传播，获取对比损失
            _, contrastive_loss = model(histograms, climate, compute_loss=True, batch_indices=county_year_indices)
            
            # 反向传播和优化
            optimizer.zero_grad()
            contrastive_loss.backward()
            
            # 梯度裁剪
            if config['clip_grad']:
                torch.nn.utils.clip_grad_norm_(model.parameters(), config['max_grad_norm'])
                
            optimizer.step()
            
            train_loss += contrastive_loss.item() * histograms.size(0)
        
        # 计算平均损失
        train_loss /= len(train_loader.dataset)
        train_losses.append(train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for histograms, climate, yields, county_year_indices in tqdm(val_loader, desc=f"Epoch {epoch+1}/{config['pretrain_epochs']} [Val]"):
                histograms = histograms.to(device)
                climate = climate.to(device)
                county_year_indices = county_year_indices.to(device)
                
                # 获取对比损失
                _, contrastive_loss = model(histograms, climate, compute_loss=True, batch_indices=county_year_indices)
                
                val_loss += contrastive_loss.item() * histograms.size(0)
        
        # 计算平均损失
        val_loss /= len(val_loader.dataset)
        val_losses.append(val_loss)
        
        # 更新学习率
        scheduler.step(val_loss)
        
        # 打印进度
        print(f"Epoch {epoch+1}/{config['pretrain_epochs']} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            
            # 确保目录存在
            os.makedirs(config['model_dir'], exist_ok=True)
            
            # 保存模型
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': val_loss,
            }, os.path.join(config['model_dir'], 'pretrained_encoders.pth'))
            
            print(f"保存最佳模型，验证损失: {val_loss:.4f}")
    
    # 绘制损失曲线
    plt.figure(figsize=(10, 5))
    plt.plot(train_losses, label='Training Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Contrastive Loss')
    plt.title('Pretraining Loss Curves')
    plt.legend()
    
    # 确保输出目录存在
    os.makedirs(config['output_dir'], exist_ok=True)
    
    # 保存图表
    plt.savefig(os.path.join(config['output_dir'], 'pretrain_loss.png'))
    plt.close()
    
    print("Pretraining completed!")
    return model

def get_optimizer(model, config):
    """根据配置创建优化器"""
    optimizer_type = config.get('optimizer_type', 'adam').lower()
    lr = config['learning_rate']
    weight_decay = config['weight_decay']
    
    if optimizer_type == 'adam':
        beta1 = config['optimizer_params']['adam']['beta1']
        beta2 = config['optimizer_params']['adam']['beta2']
        return optim.Adam(model.parameters(), lr=lr, betas=(beta1, beta2), weight_decay=weight_decay)
    
    elif optimizer_type == 'adamw':
        beta1 = config['optimizer_params']['adamw']['beta1']
        beta2 = config['optimizer_params']['adamw']['beta2']
        return optim.AdamW(model.parameters(), lr=lr, betas=(beta1, beta2), weight_decay=weight_decay)
    
    elif optimizer_type == 'sgd':
        momentum = config['optimizer_params']['sgd']['momentum']
        return optim.SGD(model.parameters(), lr=lr, momentum=momentum, weight_decay=weight_decay)
    
    else:
        raise ValueError(f"不支持的优化器类型: {optimizer_type}")

def visualize_expert_weights_by_epoch(expert_weights_data, config):
    """
    可视化每个epoch的专家权重分布
    
    参数:
    - expert_weights_data: 字典，包含每个epoch的专家权重数据
    - config: 配置字典
    """
    num_epochs = len(expert_weights_data)
    num_experts = expert_weights_data[0].shape[1]
    
    # 创建输出目录
    weights_dir = os.path.join(config['output_dir'], 'expert_weights')
    os.makedirs(weights_dir, exist_ok=True)
    
    # 保存权重数据到文件，以便稍后进行离线分析
    weights_file = os.path.join(config['output_dir'], 'expert_weights_data.pkl')
    with open(weights_file, 'wb') as f:
        pickle.dump(expert_weights_data, f)
    print(f"专家权重数据已保存至: {weights_file}")
    
    # 1. 生成每个epoch的专家平均权重柱状图
    plt.figure(figsize=(15, 10))
    for epoch in range(num_epochs):
        weights = expert_weights_data[epoch]
        expert_means = np.mean(weights, axis=0)
        expert_stds = np.std(weights, axis=0)
        
        plt.subplot(int(np.ceil(num_epochs/3)), 3, epoch+1)
        plt.bar(range(num_experts), expert_means)
        plt.errorbar(range(num_experts), expert_means, yerr=expert_stds, fmt='none', ecolor='black', capsize=5)
        plt.xlabel('Expert Index')
        plt.ylabel('Average Weight')
        plt.title(f'Epoch {epoch+1} - Expert Weight Distribution')
        plt.xticks(range(num_experts))
    
    plt.tight_layout()
    plt.savefig(os.path.join(weights_dir, 'expert_weights_by_epoch.png'))
    plt.close()
    
    # 2. 每个epoch的专家权重热图 (取前100个样本)
    num_samples = min(100, expert_weights_data[0].shape[0])
    for epoch in range(num_epochs):
        weights = expert_weights_data[epoch]
        plt.figure(figsize=(12, 8))
        sns.heatmap(weights[:num_samples], cmap='viridis', 
                   yticklabels=False, xticklabels=range(num_experts))
        plt.ylabel('Sample Index')
        plt.xlabel('Expert Index')
        plt.title(f'Epoch {epoch+1} - Expert Weight Heatmap (First {num_samples} Samples)')
        plt.tight_layout()
        plt.savefig(os.path.join(weights_dir, f'expert_weights_heatmap_epoch_{epoch+1}.png'))
        plt.close()
    
    # 3. 专家占比随epoch变化的折线图
    plt.figure(figsize=(12, 6))
    dominant_experts_by_epoch = []
    expert_percentages_by_epoch = []
    
    for epoch in range(num_epochs):
        weights = expert_weights_data[epoch]
        expert_dominance = np.argmax(weights, axis=1)
        expert_counts = np.bincount(expert_dominance, minlength=num_experts)
        expert_percentages = expert_counts / len(expert_dominance) * 100
        dominant_experts_by_epoch.append(expert_dominance)
        expert_percentages_by_epoch.append(expert_percentages)
    
    expert_percentages_by_epoch = np.array(expert_percentages_by_epoch)
    for i in range(num_experts):
        plt.plot(range(1, num_epochs+1), expert_percentages_by_epoch[:, i], 
                marker='o', label=f'Expert {i}')
    
    plt.xlabel('Epoch')
    plt.ylabel('Percentage of Samples (%)')
    plt.title('Percentage of Samples Where Each Expert is Dominant Across Epochs')
    plt.xticks(range(1, num_epochs+1))
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig(os.path.join(weights_dir, 'expert_dominance_by_epoch.png'))
    plt.close()
    
    # 4. 生成专家占比的统计表格
    stats_df = pd.DataFrame(expert_percentages_by_epoch, 
                           columns=[f'Expert {i}' for i in range(num_experts)])
    stats_df.index = [f'Epoch {i+1}' for i in range(num_epochs)]
    stats_df.to_csv(os.path.join(weights_dir, 'expert_dominance_stats.csv'))
    
    # 可视化统计表
    plt.figure(figsize=(12, 8))
    sns.heatmap(stats_df, annot=True, cmap='YlGnBu', fmt='.1f')
    plt.title('Percentage (%) of Samples Where Each Expert is Dominant by Epoch')
    plt.tight_layout()
    plt.savefig(os.path.join(weights_dir, 'expert_dominance_stats_heatmap.png'))
    plt.close()
    
    # 打印统计表
    print("\n专家占比统计表 (%):")
    print(stats_df)
    
    # 5. 计算样本专家选择稳定性 - 分析每个样本在不同epoch中是否选择相同的专家
    if num_epochs > 1:
        # 计算每个样本选择的主导专家在epochs间的变化次数
        dominant_experts = np.stack(dominant_experts_by_epoch, axis=1)  # [num_samples, num_epochs]
        changes = np.sum(dominant_experts[:, 1:] != dominant_experts[:, :-1], axis=1)
        
        plt.figure(figsize=(10, 6))
        plt.hist(changes, bins=range(num_epochs), alpha=0.7)
        plt.xlabel('Number of Expert Changes')
        plt.ylabel('Number of Samples')
        plt.title('Stability of Expert Selection Across Epochs')
        plt.xticks(range(num_epochs))
        plt.tight_layout()
        plt.savefig(os.path.join(weights_dir, 'expert_selection_stability.png'))
        plt.close()
        
        # 计算每个样本最常选择的专家
        most_common_expert = []
        for i in range(len(dominant_experts)):
            unique, counts = np.unique(dominant_experts[i], return_counts=True)
            most_common_expert.append(unique[np.argmax(counts)])
        
        most_common_expert = np.array(most_common_expert)
        most_common_counts = np.bincount(most_common_expert, minlength=num_experts)
        most_common_percentages = most_common_counts / len(most_common_expert) * 100
        
        plt.figure(figsize=(10, 6))
        plt.bar(range(num_experts), most_common_percentages)
        plt.xlabel('Expert Index')
        plt.ylabel('Percentage of Samples (%)')
        plt.title('Most Frequently Selected Expert Across All Epochs')
        plt.xticks(range(num_experts))
        plt.tight_layout()
        plt.savefig(os.path.join(weights_dir, 'most_frequent_expert.png'))
        plt.close()

def train_model(config):
    """训练模型的主函数"""
    # 设置随机种子
    set_seed(config['seed'])
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config['use_gpu'] else 'cpu')
    print(f"Using device: {device}")
    
    # 加载数据
    print("Loading data...")
    
    # 直接使用配置文件中指定的训练集和验证集年份列表
    train_years = config['train_years']
    val_years = config['val_years']
    
    print(f"Training years: {train_years}")
    print(f"Validation years: {val_years}")
    
    # 创建数据加载器
    train_loader = get_data_loader(
        base_dir=config['data_dir'],
        years=train_years,
        batch_size=config['batch_size'],
        num_bins=config['num_bins'],
        months=(config['start_month'], config['end_month']),
        shuffle=True,
        num_workers=config['num_workers'],
        histogram_norm=config['histogram_norm'],
        weather_norm=config['weather_norm'],
        return_county_year=config.get('use_contrastive', True),  # 如果使用对比学习，则返回县和年份信息
        weather_augment=config.get('weather_augment', False),  # 获取气候数据增强配置
        weather_noise_scale=config.get('weather_noise_scale', 0.05),  # 获取气候噪声尺度配置
        histogram_augment=config.get('histogram_augment', False),  # 获取直方图数据增强配置
        histogram_noise_scale=config.get('histogram_noise_scale', 0.05)  # 获取直方图噪声尺度配置
    )
    
    val_loader = get_data_loader(
        base_dir=config['data_dir'],
        years=val_years,
        batch_size=config['batch_size'],
        num_bins=config['num_bins'],
        months=(config['start_month'], config['end_month']),
        shuffle=False,
        num_workers=config['num_workers'],
        histogram_norm=config['histogram_norm'],
        weather_norm=config['weather_norm'],
        return_county_year=config.get('use_contrastive', True),  # 如果使用对比学习，则返回县和年份信息
        weather_augment=False,  # 验证集不使用数据增强
        weather_noise_scale=config.get('weather_noise_scale', 0.05),  # 获取气候噪声尺度配置
        histogram_augment=False,  # 验证集不使用直方图数据增强
        histogram_noise_scale=config.get('histogram_noise_scale', 0.05)  # 获取直方图噪声尺度配置
    )
    
    # 创建模型
    print("Creating model...")
    
    # 获取预测模式和损失函数类型
    prediction_mode = config.get('prediction_mode', 'regression')  # 'regression' 或 'classification'
    loss_type = config.get('loss_type', 'log_cosh')  # 损失函数类型
    
    print(f"Prediction mode: {prediction_mode}")
    print(f"Loss type: {loss_type}")
    
    model = MoEModel(
        num_bins=config['num_bins'],
        num_months=config['end_month'] - config['start_month'] + 1,
        num_climate_features=6,  # 气候数据有6个特征
        hidden_dim=config['hidden_dim'],
        num_experts=config['num_experts'],
        dropout=config['dropout'],
        yield_min=config.get('yield_min', 7.0),
        yield_max=config.get('yield_max', 80.0),
        num_yield_bins=config.get('num_yield_bins', 50),
        contrastive_weight=config['contrastive_weight'],  # 添加对比损失权重
        temperature=config['temperature'],  # 温度参数
        mode=prediction_mode,  # 设置预测模式
        loss_type=loss_type  # 设置损失函数类型
    ).to(device)
    
    # 如果存在预训练模型，加载预训练权重
    pretrained_path = os.path.join(config['model_dir'], 'pretrained_encoders.pth')
    if os.path.exists(pretrained_path) and config['use_pretrained']:
        print(f"加载预训练编码器权重: {pretrained_path}")
        checkpoint = torch.load(pretrained_path, map_location=device)
        
        # 加载预训练权重
        model_dict = model.state_dict()
        pretrained_dict = {k: v for k, v in checkpoint['model_state_dict'].items() 
                          if k in model_dict and 'histogram_encoder' in k or 'climate_gate' in k or 'hist_proj' in k or 'climate_proj' in k}
        
        model_dict.update(pretrained_dict)
        model.load_state_dict(model_dict)
        
        # 如果配置为冻结预训练编码器，则设置requires_grad=False
        if config['freeze_pretrained']:
            print("冻结预训练编码器参数")
            for name, param in model.named_parameters():
                if 'histogram_encoder' in name or 'climate_gate' in name or 'hist_proj' in name or 'climate_proj' in name:
                    param.requires_grad = False
    
    # 如果选择从之前的best_model.pth继续训练
    best_model_path = os.path.join(config['model_dir'], 'best_model.pth')
    if os.path.exists(best_model_path) and config.get('resume_from_checkpoint', False):
        print(f"从之前保存的模型继续训练: {best_model_path}")
        model.load_state_dict(torch.load(best_model_path, map_location=device))
        print("成功加载之前的模型权重")
    
    # 计算并打印模型参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # 定义优化器
    optimizer = get_optimizer(model, config)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, 
        mode='min', 
        factor=config['lr_factor'], 
        patience=config['lr_patience']
    )
    
    # 训练循环
    print("Starting training...")
    best_val_r2 = -float('inf')
    train_losses = []
    val_losses = []
    train_r2_scores = []
    val_r2_scores = []
    
    # 存储每个epoch的专家权重
    expert_weights_by_epoch = {}
    
    for epoch in range(config['epochs']):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_task_loss = 0.0  # 任务损失（回归或分类）
        train_contrastive_loss = 0.0
        train_preds = []
        train_targets = []
        
        # 收集当前epoch的所有样本的专家权重
        epoch_expert_weights = []
        
        # 设置固定顺序的数据加载器，用于专家权重跟踪
        # 注意：这里我们创建一个新的数据加载器，使用相同的数据但是不进行随机打乱
        # 这样可以确保每个epoch中样本的顺序是一致的，便于跟踪权重变化
        if epoch == 0:
            # 第一个epoch，创建跟踪数据加载器
            tracking_loader = get_data_loader(
                base_dir=config['data_dir'],
                years=train_years,
                batch_size=config['batch_size'],
                num_bins=config['num_bins'],
                months=(config['start_month'], config['end_month']),
                shuffle=False,  # 不随机打乱，保持顺序一致
                num_workers=config['num_workers'],
                histogram_norm=config['histogram_norm'],
                weather_norm=config['weather_norm'],
                return_county_year=False,
                weather_augment=False,  # 不使用数据增强
                histogram_augment=False  # 不使用数据增强
            )
        
        # 在训练前先收集专家权重
        print(f"收集epoch {epoch+1}的专家权重数据...")
        model.eval()  # 切换到评估模式，避免dropout影响权重
        with torch.no_grad():
            for histograms, climate, yields in tqdm(tracking_loader, desc=f"Collecting Weights for Epoch {epoch+1}"):
                histograms = histograms.to(device)
                climate = climate.to(device)
                
                # 获取专家权重
                gate_weights, _ = model.get_expert_weights(histograms, climate)
                epoch_expert_weights.append(gate_weights.cpu().numpy())
        
        # 保存当前epoch的专家权重
        epoch_expert_weights = np.vstack(epoch_expert_weights)
        expert_weights_by_epoch[epoch] = epoch_expert_weights
        
        # 恢复训练模式
        model.train()
        
        # 正常训练循环
        for batch_data in tqdm(train_loader, desc=f"Epoch {epoch+1}/{config['epochs']} [Train]"):
            # 处理带有县和年份信息的批次数据
            if len(batch_data) == 4:  # 如果数据加载器返回了县和年份信息
                histograms, climate, yields, county_year_indices = batch_data
                county_year_indices = county_year_indices.to(device)
            else:  # 否则使用None作为县和年份信息
                histograms, climate, yields = batch_data
                county_year_indices = None
            
            histograms = histograms.to(device)
            climate = climate.to(device)
            yields = yields.to(device)
            
            # 前向传播，获取预测值、对比损失和任务损失（回归或分类）
            outputs, contrastive_loss, task_loss = model(
                histograms, climate, 
                compute_loss=True, 
                batch_indices=county_year_indices,
                target_yields=yields,
                use_contrastive=config.get('use_contrastive', False)
            )
            
            # 合并损失
            if config.get('use_contrastive', False):
                loss = task_loss + model.contrastive_weight * contrastive_loss
            else:
                loss = task_loss
                contrastive_loss = torch.tensor(0.0).to(device)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            if config['clip_grad']:
                torch.nn.utils.clip_grad_norm_(model.parameters(), config['max_grad_norm'])
                
            optimizer.step()
            
            train_loss += loss.item() * histograms.size(0)
            train_task_loss += task_loss.item() * histograms.size(0)
            train_contrastive_loss += contrastive_loss.item() * histograms.size(0)
            train_preds.extend(outputs.detach().cpu().numpy())
            train_targets.extend(yields.detach().cpu().numpy())
        
        # 计算平均损失
        train_loss /= len(train_loader.dataset)
        train_task_loss /= len(train_loader.dataset)
        train_contrastive_loss /= len(train_loader.dataset)
        train_r2 = r2_score(np.array(train_targets), np.array(train_preds))
        train_losses.append(train_loss)
        train_r2_scores.append(train_r2)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_task_loss = 0.0
        val_contrastive_loss = 0.0
        val_preds = []
        val_targets = []
        
        with torch.no_grad():
            for batch_data in tqdm(val_loader, desc=f"Epoch {epoch+1}/{config['epochs']} [Val]"):
                # 处理带有县和年份信息的批次数据
                if len(batch_data) == 4:  # 如果数据加载器返回了县和年份信息
                    histograms, climate, yields, county_year_indices = batch_data
                    county_year_indices = county_year_indices.to(device)
                else:  # 否则使用None作为县和年份信息
                    histograms, climate, yields = batch_data
                    county_year_indices = None
                
                histograms = histograms.to(device)
                climate = climate.to(device)
                yields = yields.to(device)
                
                # 前向传播，获取预测值、对比损失和任务损失（回归或分类）
                outputs, contrastive_loss, task_loss = model(
                    histograms, climate, 
                    compute_loss=True, 
                    batch_indices=county_year_indices,
                    target_yields=yields,
                    use_contrastive=config.get('use_contrastive', False)
                )
                
                # 合并损失
                if config.get('use_contrastive', False):
                    loss = task_loss + model.contrastive_weight * contrastive_loss
                else:
                    loss = task_loss
                    contrastive_loss = torch.tensor(0.0).to(device)
                
                val_loss += loss.item() * histograms.size(0)
                val_task_loss += task_loss.item() * histograms.size(0)
                val_contrastive_loss += contrastive_loss.item() * histograms.size(0)
                val_preds.extend(outputs.detach().cpu().numpy())
                val_targets.extend(yields.detach().cpu().numpy())
        
        # 计算平均损失
        val_loss /= len(val_loader.dataset)
        val_task_loss /= len(val_loader.dataset)
        val_contrastive_loss /= len(val_loader.dataset)
        val_r2 = r2_score(np.array(val_targets), np.array(val_preds))
        val_losses.append(val_loss)
        val_r2_scores.append(val_r2)
        
        # 更新学习率
        scheduler.step(val_loss)
        
        # 任务类型名称，用于打印
        task_name = "Task" if prediction_mode == 'regression' else "Cls"
        
        # 打印统计信息
        if config.get('use_contrastive', False):
            print(f"Epoch {epoch+1}/{config['epochs']} - "
                  f"Train Loss: {train_loss:.4f} ({task_name}: {train_task_loss:.4f}, Cont: {train_contrastive_loss:.4f}), Train R²: {train_r2:.4f}, "
                  f"Val Loss: {val_loss:.4f} ({task_name}: {val_task_loss:.4f}, Cont: {val_contrastive_loss:.4f}), Val R²: {val_r2:.4f}")
        else:
            print(f"Epoch {epoch+1}/{config['epochs']} - "
                  f"Train Loss: {train_loss:.4f} ({task_name}: {train_task_loss:.4f}), Train R²: {train_r2:.4f}, "
                  f"Val Loss: {val_loss:.4f} ({task_name}: {val_task_loss:.4f}), Val R²: {val_r2:.4f}")
        
        # 保存最佳模型
        if val_r2 > best_val_r2:
            best_val_r2 = val_r2
            if not os.path.exists(config['model_dir']):
                os.makedirs(config['model_dir'])
            torch.save(model.state_dict(), os.path.join(config['model_dir'], 'best_model.pth'))
            print(f"Saved new best model with validation R² of {val_r2:.4f}")
    
    # 可视化每个epoch的专家权重
    print("Visualizing expert weights by epoch...")
    visualize_expert_weights_by_epoch(expert_weights_by_epoch, config)
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Loss Curves')
    
    plt.subplot(1, 2, 2)
    plt.plot(train_r2_scores, label='Train R²')
    plt.plot(val_r2_scores, label='Validation R²')
    plt.xlabel('Epoch')
    plt.ylabel('R²')
    plt.legend()
    plt.title('R² Curves')
    
    plt.tight_layout()
    
    # 保存图表
    if not os.path.exists(config['output_dir']):
        os.makedirs(config['output_dir'])
    plt.savefig(os.path.join(config['output_dir'], 'training_curves.png'))
    plt.close()
    
    print(f"Training completed. Best validation R²: {best_val_r2:.4f}")
    
    # 返回最佳验证R²
    return best_val_r2

def evaluate_model(config):
    """评估模型的函数"""
    # 设置随机种子
    set_seed(config['seed'])
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config['use_gpu'] else 'cpu')
    print(f"Using device: {device}")
    
    # 加载测试数据
    print("Loading test data...")
    
    # 判断test_years是整数还是列表
    if isinstance(config['test_years'], list):
        # 如果是列表，则直接使用
        test_years = config['test_years']
    else:
        # 如果是整数，则使用原有逻辑
        test_years = list(range(config['start_year'], config['end_year'] + 1))[-config['test_years']:]
    
    print(f"Test years: {test_years}")
    
    test_loader = get_data_loader(
        base_dir=config['data_dir'],
        years=test_years,
        batch_size=config['batch_size'],
        num_bins=config['num_bins'],
        months=(config['start_month'], config['end_month']),
        shuffle=False,
        num_workers=config['num_workers'],
        histogram_norm=config['histogram_norm'],
        weather_norm=config['weather_norm'],
        weather_augment=False,  # 测试集不使用数据增强
        weather_noise_scale=config.get('weather_noise_scale', 0.05),  # 获取气候噪声尺度配置
        histogram_augment=False,  # 测试集不使用直方图数据增强
        histogram_noise_scale=config.get('histogram_noise_scale', 0.05)  # 获取直方图噪声尺度配置
    )
    
    # 获取预测模式和损失函数类型
    prediction_mode = config.get('prediction_mode', 'regression')  # 'regression' 或 'classification'
    loss_type = config.get('loss_type', 'log_cosh')  # 损失函数类型
    
    print(f"Prediction mode: {prediction_mode}")
    print(f"Loss type: {loss_type}")
    
    # 创建模型并加载最佳权重
    print("Loading best model...")
    model = MoEModel(
        num_bins=config['num_bins'],
        num_months=config['end_month'] - config['start_month'] + 1,
        num_climate_features=6,  # 气候数据有6个特征
        hidden_dim=config['hidden_dim'],
        num_experts=config['num_experts'],
        dropout=0.0,  # 评估时不使用dropout
        yield_min=config.get('yield_min', 7.0),
        yield_max=config.get('yield_max', 80.0),
        num_yield_bins=config.get('num_yield_bins', 50),
        mode=prediction_mode,
        loss_type=loss_type
    ).to(device)
    
    model.load_state_dict(torch.load(os.path.join(config['model_dir'], 'best_model.pth')))
    model.eval()
    
    # 评估模型
    test_preds = []
    test_targets = []
    
    with torch.no_grad():
        for histograms, climate, yields in tqdm(test_loader, desc="Evaluating"):
            histograms = histograms.to(device)
            climate = climate.to(device)
            yields = yields.to(device)
            
            if prediction_mode == 'regression':
                # 回归模式直接获取预测值
                outputs = model(histograms, climate)
            else:
                # 分类模式先获取logits，然后计算期望值
                logits = model(histograms, climate)
                # 计算每个箱的概率
                probs = F.softmax(logits, dim=-1)  # [B, num_yield_bins]
                
                # 检查是否使用概率最高的bin的中位数
                use_max_prob_bin = config.get('use_max_prob_bin', False)
                
                if use_max_prob_bin:
                    # 找到概率最高的bin的索引
                    max_prob_indices = torch.argmax(probs, dim=1)  # [B]
                    # 获取对应bin的中位数
                    outputs = model.bin_centers[max_prob_indices].unsqueeze(1)  # [B, 1]
                else:
                    # 计算期望值作为预测产量
                    outputs = torch.sum(model.bin_centers.unsqueeze(0) * probs, dim=1, keepdim=True)  # [B, 1]
            
            test_preds.extend(outputs.detach().cpu().numpy())
            test_targets.extend(yields.detach().cpu().numpy())
    
    # 计算R²分数
    test_r2 = r2_score(np.array(test_targets), np.array(test_preds))
    
    # 显示使用的预测方法
    if prediction_mode == 'classification':
        prediction_method = "概率最高bin的中位数" if config.get('use_max_prob_bin', False) else "加权平均期望值"
        print(f"分类预测方法: {prediction_method}")
    
    print(f"Test R²: {test_r2:.4f}")
    
    # 绘制预测vs实际值的散点图
    plt.figure(figsize=(10, 6))
    plt.scatter(test_targets, test_preds, alpha=0.5)
    plt.plot([min(test_targets), max(test_targets)], [min(test_targets), max(test_targets)], 'r--')
    plt.xlabel('Actual Yield')
    plt.ylabel('Predicted Yield')
    plt.title(f'Actual vs Predicted Yield (R² = {test_r2:.4f})')
    
    # 保存图表
    if not os.path.exists(config['output_dir']):
        os.makedirs(config['output_dir'])
    plt.savefig(os.path.join(config['output_dir'], 'prediction_scatter.png'))
    plt.close()
    
    return test_r2

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练或评估作物产量预测模型')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--mode', type=str, choices=['train', 'evaluate', 'pretrain'], default='train', help='运行模式：训练、评估或预训练')
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 根据模式执行相应操作
    if args.mode == 'train':
        train_model(config)
    elif args.mode == 'evaluate':
        evaluate_model(config)
    elif args.mode == 'pretrain':
        pretrain_encoders(config)

if __name__ == '__main__':
    main()
