# 数据配置
data_dir: "datasets"  # 数据根目录
start_year: 2017  # 起始年份
end_year: 2022  # 结束年份
start_month: 3  # 起始月份
end_month: 10  # 结束月份
num_bins: 32  # 直方图bin数量
histogram_norm: "std"  # 直方图归一化方式: None, 'prob', 'minmax', 'std'
weather_norm: "minmax"  # 气候数据归一化方式: None, 'minmax', 'std'
weather_augment: true  # 是否对气候数据进行噪声增强
weather_noise_scale: 0.1  # 气候数据噪声增强的标准差大小
histogram_augment: true  # 是否对直方图数据进行噪声增强
histogram_noise_scale: 0.1  # 直方图数据噪声增强的标准差大小

# 产量预测配置
prediction_mode: "classification"  # 预测模式: "regression" 或 "classification"
loss_type: "ce"  # 损失函数类型

# 回归损失函数选项: 
# - "log_cosh": log(cosh(y_pred - y_true))
# - "huber": Huber损失，结合MSE和MAE的优点
# - "mse": 均方误差损失
# - "mae": 平均绝对误差损失

# 分类损失函数选项:
# - "ce": 标准交叉熵损失
# - "focal": Focal Loss，对难分类样本给予更高权重
# - "label_smoothing": 标签平滑交叉熵，提高泛化能力

# 产量分类配置 (用于分类模式)
yield_min: 7.0  # 产量最小值
yield_max: 80.0  # 产量最大值
num_yield_bins: 100  # 产量分箱数量
use_max_prob_bin: true  # 是否使用概率最高的bin的中位数来预测产量，而不是加权平均

# 数据集划分
train_years: [2017, 2018, 2019, 2020, 2021]  # 训练集年份列表
val_years: [2022]  # 验证集年份列表
test_years: [2022]  #: 测试集年份列表

# 预训练数据集划分
pretrain_train_years: [2017, 2018, 2019, 2020, 2021, 2022]  # 预训练训练集年份
pretrain_val_years: [2022]  # 预训练验证集年份

# 模型配置
hidden_dim: 128  # 隐藏层维度
expert_hidden_dim: 200  # 专家网络的隐藏层维度，默认为hidden_dim的2倍
num_experts: 4  # 专家数量 (现在是独立的超参数，不再必须与通道数量一致)
num_blocks: 2  # 每个Stage中的Block数量
dropout: 0.2  # Dropout比率
contrastive_weight: 0.1  # 对比损失权重
temperature: 0.07  # 对比学习温度参数
use_contrastive: false  # 是否在标准训练中使用对比学习

# 预训练配置
pretrain_epochs: 20  # 预训练轮数
pretrain_lr: 0.0005  # 预训练学习率
use_pretrained: false  # 是否使用预训练模型
freeze_pretrained: false  # 是否冻结预训练编码器参数

# 训练配置
seed: 3407  # 随机种子
batch_size: 64  # 批次大小
epochs: 20  # 训练轮数
learning_rate: 0.005  # 学习率
weight_decay: 0.005  # 权重衰减
clip_grad: true  # 是否进行梯度裁剪
max_grad_norm: 1.0  # 梯度裁剪阈值
lr_factor: 0.4  # 学习率衰减因子
lr_patience: 4  # 学习率调整耐心值
use_gpu: true  # 是否使用GPU
num_workers: 4  # 数据加载的工作进程数
resume_from_checkpoint: false  # 是否从之前保存的best_model.pth继续训练

# 优化器配置
optimizer_type: "adamw"  # 优化器类型: "adam", "adamw", "sgd"
optimizer_params:  # 优化器特定参数
  adam:
    beta1: 0.9  # Adam优化器的beta1参数
    beta2: 0.999  # Adam优化器的beta2参数
  adamw:
    beta1: 0.9  # AdamW优化器的beta1参数
    beta2: 0.999  # AdamW优化器的beta2参数
  sgd:
    momentum: 0.9  # SGD优化器的动量参数

# 路径配置
model_dir: "saved_models"  # 模型保存目录
output_dir: "outputs"  # 输出目录
