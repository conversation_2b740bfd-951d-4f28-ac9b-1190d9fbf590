# 可以考虑改变一下时间点的选取（从65到297），把时间点的选取作为参数
import os
import numpy as np
import rasterio

def convert_to_histogram(tif_path, num_bins=80, value_range=(0, 8000)):
    """
    将tif文件转换为直方图数据
    Args:
        tif_path: tif文件路径或目录路径
        num_bins: 直方图的分箱数
        value_range: 像素值的范围
    Returns:
        dict: 以fipscode为键，直方图数据为值的字典
    """
    # 计算bin的边界
    bins = np.linspace(value_range[0], value_range[1], num_bins + 1)
    results = {}
    
    # 判断输入路径是文件还是目录
    if os.path.isfile(tif_path):
        # 如果是单个文件
        fipscode = os.path.splitext(os.path.basename(tif_path))[0]
        with rasterio.open(tif_path) as src:
            # 读取数据
            data = src.read()
            height, width = data.shape[1], data.shape[2]
            data = data.reshape(46, 14, height, width)
            
            # 选择时间范围和通道
            time_slice = slice(8, 37+1)
            channel_slice = slice(0, 6)
            selected_data = data[time_slice, channel_slice, :, :]
            
            if num_bins == 1:
                # 对于num_bins=1的情况，直接计算平均值
                # 创建存储平均值的数组(时间点, 通道)
                avg_data = np.zeros((20, 6))
                
                for t in range(20):
                    for c in range(6):
                        current_data = selected_data[t, c]
                        # 先创建掩码，只选择在范围内的值
                        valid_mask = (current_data >= value_range[0]) & (current_data <= value_range[1])
                        valid_data = current_data[valid_mask]
                        
                        # 计算平均值
                        if valid_data.size > 0:
                            avg_data[t, c] = np.mean(valid_data)
                        else:
                            avg_data[t, c] = 0  # 如果没有有效值，设为0
                
                results[fipscode] = avg_data
            else:
                # 创建存储直方图数据的数组
                histogram_data = np.zeros((20, 6, num_bins))
                
                # 对每个时间点和通道计算直方图
                for t in range(20):
                    for c in range(6):
                        current_data = selected_data[t, c]
                        # 先创建掩码，只选择在范围内的值
                        valid_mask = (current_data >= value_range[0]) & (current_data <= value_range[1])
                        current_data = current_data[valid_mask]
                        
                        # 计算直方图
                        hist, _ = np.histogram(current_data, bins=bins)
                        histogram_data[t, c] = hist
                
                results[fipscode] = histogram_data
    
    else:
        # 如果是目录，获取所有tif文件
        tif_files = [f for f in os.listdir(tif_path) if f.endswith('.tif')]
        
        for tif_file in tif_files:
            fipscode = tif_file.split('.')[0]
            file_path = os.path.join(tif_path, tif_file)
            print(f"处理文件: {tif_file}")
            
            with rasterio.open(file_path) as src:
                data = src.read()
                height, width = data.shape[1], data.shape[2]
                data = data.reshape(46, 14, height, width)
                
                time_slice = slice(8, 37+1)
                channel_slice = slice(0, 6)
                selected_data = data[time_slice, channel_slice, :, :]
                
                if num_bins == 1:
                    # 对于num_bins=1的情况，直接计算平均值
                    avg_data = np.zeros((20, 6))
                    
                    for t in range(20):
                        for c in range(6):
                            current_data = selected_data[t, c]
                            # 先创建掩码，只选择在范围内的值
                            valid_mask = (current_data >= value_range[0]) & (current_data <= value_range[1])
                            valid_data = current_data[valid_mask]
                            
                            # 计算平均值
                            if valid_data.size > 0:
                                avg_data[t, c] = np.mean(valid_data)
                            else:
                                avg_data[t, c] = 0  # 如果没有有效值，设为0
                    
                    results[fipscode] = avg_data
                else:
                    histogram_data = np.zeros((20, 6, num_bins))
                    
                    for t in range(20):
                        for c in range(6):
                            current_data = selected_data[t, c]
                            # 先创建掩码，只选择在范围内的值
                            valid_mask = (current_data >= value_range[0]) & (current_data <= value_range[1])
                            current_data = current_data[valid_mask]
                            
                            # 计算直方图
                            hist, _ = np.histogram(current_data, bins=bins)
                            histogram_data[t, c] = hist
                    
                    results[fipscode] = histogram_data
    
    return results

def convert_and_save_histograms(tif_path, save_dir, year, num_bins=128, value_range=(2000, 5000), aggregate=False):
    """
    将tif文件转换为直方图数据并保存
    Args:
        tif_path: tif文件路径或目录路径
        save_dir: 保存结果的目录
        year: 数据的年份
        num_bins: 直方图的分箱数
        value_range: 像素值的范围
        aggregate: 是否将所有县的数据聚合到一个文件中
    """
    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)
    
    # 获取直方图数据
    histogram_results = convert_to_histogram(tif_path, num_bins=num_bins, value_range=value_range)
    
    if aggregate:
        # 聚合所有县的数据到一个文件
        aggregate_and_save_histograms(histogram_results, save_dir, year, num_bins, value_range)
    else:
        # 将元数据分开保存，避免使用Python对象
        for fipscode, hist_data in histogram_results.items():
            # 构建文件名：year_fipscode_histogram.npz
            filename = f"{year}_{fipscode}_histogram.npz"
            save_path = os.path.join(save_dir, filename)
            
            # 保存数据和元数据
            np.savez(save_path,
                    histogram=hist_data,  # 当num_bins=1时，shape为(20, 6)；否则为(20, 6, num_bins)
                    year=year,
                    num_bins=num_bins,
                    value_range_min=value_range[0],
                    value_range_max=value_range[1],
                    time_range_start=65, # DOY 65 is 2020-03-01
                    time_range_end=297, # DOY 297 is 2020-10-23
                    channels=6,
                    time_points=20)
            print(f"已保存 {filename}")

def aggregate_and_save_histograms(histogram_results, save_dir, year, num_bins, value_range):
    """
    将所有县的直方图数据聚合到一个文件中
    Args:
        histogram_results: 以fipscode为键，直方图数据为值的字典
        save_dir: 保存结果的目录
        year: 数据的年份
        num_bins: 直方图的分箱数
        value_range: 像素值的范围
    """
    if not histogram_results:
        print("没有数据可聚合")
        return
    
    # 获取所有fipscode并排序，确保顺序一致
    fipscodes = sorted(list(histogram_results.keys()))
    
    # 创建一个数组来存储所有县的直方图数据
    # 根据num_bins设置适当的形状
    all_histograms = np.array([histogram_results[fips] for fips in fipscodes])
    
    # 构建聚合文件名
    filename = f"{year}_all_counties_histogram_{num_bins}.npz"
    save_path = os.path.join(save_dir, filename)
    
    # 保存聚合数据和元数据
    np.savez(save_path,
             histograms=all_histograms,  # 当num_bins=1时，shape为(counties, 20, 6)；否则为(counties, 20, 6, num_bins)
             fipscodes=fipscodes,  # 保存fipscode列表，与histograms的第一维对应
             year=year,
             num_bins=num_bins,
             value_range_min=value_range[0],
             value_range_max=value_range[1],
             time_range_start=65,
             time_range_end=297,
             channels=6,
             time_points=20)
    
    print(f"已聚合并保存所有县的数据到 {filename}")
    print(f"共包含 {len(fipscodes)} 个县的数据")

if __name__ == "__main__":
    # 基础路径设置
    base_tif_path = r"E:\my_crop\datasets"
    save_dir = r"E:\my_crop\datasets\histograms"
    val_range = (200, 5000)
    num_bins = 1
    
    # 处理2017-2022年的数据
    for year in range(2017, 2023):
        print(f"\n处理 {year} 年的数据...")
        tif_path = os.path.join(base_tif_path, f"soybeans_{year}")
        
        # 检查目录是否存在
        if not os.path.exists(tif_path):
            print(f"警告：{year}年的数据目录不存在，跳过...")
            continue
            
        # 执行转换和保存，设置aggregate=True来聚合所有县的数据
        convert_and_save_histograms(tif_path, save_dir, year, 
                                  num_bins=num_bins, 
                                  value_range=val_range, 
                                  aggregate=True)
        print(f"{year}年数据处理完成")
    
    print("\n所有年份数据处理完成！")
