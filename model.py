import torch
import torch.nn as nn
import torch.nn.functional as F


class ConvBlock(nn.Module):
    """
    卷积块，包含卷积层、批归一化和激活函数
    """
    def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0, dilation=1, groups=1, bias=True, act=True):
        super(ConvBlock, self).__init__()
        self.conv = nn.Conv1d(in_channels, out_channels, kernel_size, stride, padding, dilation, groups, bias)
        self.bn = nn.BatchNorm1d(out_channels)
        self.act = nn.GELU() if act else nn.Identity()

    def forward(self, x):
        return self.act(self.bn(self.conv(x)))

class LightTSBlock(nn.Module):
    """
    轻量级时序处理模块，用于替代Stage中的Block
    专门设计用于处理多元时序序列，关注参数效率
    """
    def __init__(self, dmodel, nvars, time_len, dropout=0.1):
        super(LightTSBlock, self).__init__()
        
        # dmodel: 特征维度 (num_bins)
        # nvars: 变量个数 (M=6)
        # time_len: 时间长度 (N//4=5)
        
        # 时间维度卷积 - 捕获时间上的依赖关系
        self.time_conv = nn.Conv2d(
            in_channels=nvars,
            out_channels=nvars,
            kernel_size=(1, 3),  # 只在时间维度上进行卷积
            padding=(0, 1),
            padding_mode='zeros',
            groups=nvars  # 深度可分离卷积减少参数
        )
        
        # 特征维度卷积 - 捕获特征间的交互
        self.feature_mix = nn.Sequential(
            nn.Conv2d(nvars, nvars, kernel_size=1),  # 1x1卷积
            nn.GELU()
        )
        # 变量处理放在时间处理之前
        
        
        # 简化的门控机制
        self.gate = nn.Sequential(
            nn.Conv2d(nvars, nvars, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 标准化和dropout
        self.norm = nn.LayerNorm([dmodel, time_len])
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        # x: [B, M, D, N]
        # M: 变量个数 (6)
        # D: 特征维度 (num_bins)
        # N: 时间长度 (5)
        
        # 获取残差连接
        residual = x
        
        # 时间维度卷积
        x_time = self.time_conv(x.permute(0, 1, 3, 2))  # [B, M, N, D] -> [B, M, N, D]
        
        # 特征混合
        x_feature = self.feature_mix(x.permute(0, 1, 3, 2))  # [B, M, N, D] -> [B, M, N, D]
        
        # 门控机制
        gate = self.gate(x.permute(0, 1, 3, 2))  # [B, M, N, D] -> [B, M, N, D]
        
        # 组合特征 (门控)
        x = x_time * gate + x_feature * (1 - gate)
        
        # 残差连接
        x = x + residual.permute(0, 1, 3, 2)  # [B, M, D, N] -> [B, M, N, D]
        
        # 归一化 (只对特征维度和时间维度归一化)
        x = x.permute(0, 1, 3, 2)  # [B, M, N, D] -> [B, M, D, N]
        x = self.norm(x)
        x = x.permute(0, 1, 3, 2)  # [B, M, D, N] -> [B, M, N, D]
        
        # Dropout
        x = self.dropout(x)
        
        return x.permute(0, 1, 3, 2)  # [B, M, N, D] -> [B, M, D, N]

class LightTSStage(nn.Module):
    """
    轻量级时序处理Stage，替代原ModernTCN的Stage
    专门设计用于处理多元时序序列数据
    """
    def __init__(self, num_blocks, dmodel, nvars, time_len, dropout=0.1):
        super(LightTSStage, self).__init__()
        
        # 创建多个块
        self.blocks = nn.ModuleList([
            LightTSBlock(
                dmodel=dmodel,
                nvars=nvars,
                time_len=time_len,
                dropout=dropout
            ) for _ in range(num_blocks)
        ])
        
    def forward(self, x):
        # 逐块处理
        for block in self.blocks:
            x = block(x)
        return x

class HistogramEncoder(nn.Module):
    """
    直方图编码器，使用多阶段下采样和轻量级时序处理模块
    """
    def __init__(self, num_bins=64, hidden_dim=128, num_blocks=2, dropout=0.1):
        super(HistogramEncoder, self).__init__()

        # 输入形状为 [B, N, M, num_bins]，其中：
        # N = 20 (时间步长)
        # M = 6 (特征通道)
        self.nvars = 6  # 变量数量（特征通道）
        self.hidden_dim = hidden_dim

        # 第一次时间维度下采样：20->10
        self.downsample1 = ConvBlock(in_channels=num_bins, out_channels=num_bins, kernel_size=2, stride=2)

        # 第一阶段时序处理
        self.stage1 = LightTSStage(
            num_blocks=num_blocks,
            dmodel=num_bins,
            nvars=self.nvars,
            time_len=10,  # 第一次下采样后的时间长度 (20/2=10)
            dropout=dropout
        )

        # 第二次时间维度下采样：10->5
        self.downsample2 = ConvBlock(in_channels=num_bins, out_channels=num_bins, kernel_size=2, stride=2)

        # 第二阶段时序处理
        self.stage2 = LightTSStage(
            num_blocks=num_blocks,
            dmodel=num_bins,
            nvars=self.nvars,
            time_len=5,  # 第二次下采样后的时间长度 (10/2=5)
            dropout=dropout
        )

        # 最后时间维度下采样：5->1
        self.downsample3 = ConvBlock(in_channels=num_bins, out_channels=hidden_dim, kernel_size=5, stride=5)

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # x: [B, N=20, M=6, num_bins]
        B, N, M, num_bins = x.shape
        
        # 调整维度以进行时间维度下采样
        x = x.permute(0, 2, 3, 1)  # [B, M, num_bins, N=20]
        x = x.reshape(B*M, num_bins, N)  # [B*M, num_bins, N=20]
        
        # 第一次下采样
        x = self.downsample1(x)  # [B*M, num_bins, N=10]
        
        # 重塑为第一阶段Stage所需的格式
        x = x.reshape(B, M, num_bins, N//2)  # [B, M, num_bins, N=10]
        
        # 应用第一阶段Stage
        x = self.stage1(x)  # [B, M, num_bins, N=10]
        
        # 准备第二次下采样
        x = x.reshape(B*M, num_bins, N//2)  # [B*M, num_bins, N=10]
        
        # 第二次下采样
        x = self.downsample2(x)  # [B*M, num_bins, N=5]
        
        # 重塑为第二阶段Stage所需的格式
        x = x.reshape(B, M, num_bins, N//4)  # [B, M, num_bins, N=5]
        
        # 应用第二阶段Stage
        x = self.stage2(x)  # [B, M, num_bins, N=5]
        
        # 准备最后下采样
        x = x.reshape(B*M, num_bins, N//4)  # [B*M, num_bins, N=5]
        
        # 最后下采样
        x = self.downsample3(x)  # [B*M, hidden_dim, 1]
        
        # 压缩和重塑
        x = x.squeeze(-1)  # [B*M, hidden_dim]
        
        # 重塑为最终输出格式
        x = x.reshape(B, M, self.hidden_dim)  # [B, M=6, hidden_dim]
        
        # 保存中间表示用于对比损失
        self.mid_representation = x
        
        # 在中间维度上进行平均池化，而不是分离通道
        x = torch.mean(x, dim=1)  # [B, hidden_dim]
        
        return x

class ClimateGate(nn.Module):
    """
    气候门控网络，处理气候数据并生成专家融合权重
    使用时序模型捕捉气候数据的时序关系
    """
    def __init__(self, num_months=8, num_features=6, hidden_dim=128, num_experts=6, dropout=0.1):
        super(ClimateGate, self).__init__()

        # 处理形状为 [B, num_months, num_features] 的气候数据

        # 第一层1D卷积，步长为2
        self.conv1 = ConvBlock(in_channels=num_features, out_channels=hidden_dim//2, kernel_size=2, stride=2)
        

        # 第二层1D卷积，步长为2
        self.conv2 = ConvBlock(in_channels=hidden_dim//2, out_channels=hidden_dim, kernel_size=2, stride=2)
        
        # 第三层1D卷积，步长为2
        self.conv3 = ConvBlock(in_channels=hidden_dim, out_channels=hidden_dim, kernel_size=2, stride=2)
        
        

        # Dropout
        self.dropout = nn.Dropout(dropout)

        # 门控输出层
        self.gate = nn.Linear(hidden_dim, num_experts)
        
        

    def forward(self, x):
        # x: [B, num_months, num_features]
        # 调整输入维度
        x = x.permute(0, 2, 1)  # [B, num_features, num_months]

        # 第一层1D卷积，步长为2
        x = self.conv1(x)  # [B, hidden_dim//2, num_months//2]
        

        # 第二层卷积
        x = self.conv2(x)  # [B, hidden_dim, num_months//4]
        

        # 第三层卷积
        x = self.conv3(x)  # [B, hidden_dim, num_months//8 = 1]
        
        
        x = x.squeeze(-1)  # [B, hidden_dim]
        
        # 保存中间表示用于对比损失 - 只克隆不分离计算图
        representation = x.clone()  # 移除detach()

        # Dropout
        x = self.dropout(x)

        # 门控层
        x = self.gate(x)  # [B, num_experts]

        # Softmax归一化权重
        x = F.softmax(x, dim=-1)  # [B, num_experts]

        return x, representation

class Expert(nn.Module):
    """
    专家网络，每个专家处理一个特定的通道
    可以根据模式输出回归值或分类logits
    """
    def __init__(self, input_dim=128, hidden_dim=None, num_yield_bins=50, dropout=0.1, mode='regression'):
        super(Expert, self).__init__()
        
        self.mode = mode
        
        # 如果未指定隐藏层维度，则默认为输入维度的2倍
        if hidden_dim is None:
            hidden_dim = input_dim * 2
        
        # 共享的特征提取层
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        # 回归输出头
        self.regression_head = nn.Linear(hidden_dim, 1)
        
        # 分类输出头
        self.classification_head = nn.Linear(hidden_dim, num_yield_bins)

    def forward(self, x):
        # x: [B, input_dim]
        features = self.feature_extractor(x)
        
        if self.mode == 'regression':
            return self.regression_head(features)  # [B, 1]
        else:  # classification
            return self.classification_head(features)  # [B, num_yield_bins]

class MoEModel(nn.Module):
    """
    混合专家模型，每个通道对应一个专家
    支持回归和分类两种模式
    """
    def __init__(self,
                 num_bins=64,
                 num_months=8,
                 num_climate_features=6,
                 hidden_dim=128,
                 expert_hidden_dim=None,  # 新增参数：专家网络的隐藏层维度
                 num_blocks=2,
                 num_experts=6,
                 dropout=0.1,
                 yield_min=7.0,
                 yield_max=80.0,
                 num_yield_bins=50,
                 contrastive_weight=0.1,
                 temperature=0.07,
                 pretrain_mode=False,
                 mode='regression',  # 'regression' 或 'classification'
                 loss_type='log_cosh'  # 损失函数类型
                 ):
        super(MoEModel, self).__init__()
        
        # 保存产量相关参数 (用于分类模式)
        self.yield_min = yield_min
        self.yield_max = yield_max
        self.num_yield_bins = num_yield_bins
        
        # 计算每个箱的边界和中心值 (用于分类模式)
        self.register_buffer('bin_edges', torch.linspace(yield_min, yield_max, num_yield_bins + 1))
        self.register_buffer('bin_centers', (self.bin_edges[:-1] + self.bin_edges[1:]) / 2)
        
        # 保存对比损失权重和温度参数
        self.contrastive_weight = contrastive_weight
        self.temperature = temperature
        self.pretrain_mode = pretrain_mode
        
        # 保存预测模式和损失函数类型
        self.mode = mode
        self.loss_type = loss_type

        # 直方图编码器
        self.histogram_encoder = HistogramEncoder(
            num_bins=num_bins,
            hidden_dim=hidden_dim,
            num_blocks=num_blocks,
            dropout=dropout
        )

        # 气候门控网络 - 生成6个权重
        self.climate_gate = ClimateGate(
            num_months=num_months,
            num_features=num_climate_features,
            hidden_dim=hidden_dim,
            num_experts=num_experts,  # 修改为6个专家
            dropout=dropout
        )

        # 6个专家网络，每个对应一个通道
        self.experts = nn.ModuleList([
            Expert(
                input_dim=hidden_dim,
                hidden_dim=expert_hidden_dim,  # 使用专门的expert_hidden_dim参数
                num_yield_bins=num_yield_bins,
                dropout=dropout,
                mode=mode
            ) for _ in range(num_experts)  # 6个专家
        ])
    
    def set_mode(self, mode):
        """设置预测模式: 'regression' 或 'classification'"""
        self.mode = mode
        for expert in self.experts:
            expert.mode = mode
    
    def set_loss_type(self, loss_type):
        """设置损失函数类型"""
        self.loss_type = loss_type
    
    def get_yield_bin(self, yields):
        """将实际产量转换为箱索引 (用于分类模式)"""
        yields = torch.clamp(yields, self.yield_min, self.yield_max)
        bins = torch.bucketize(yields, self.bin_edges) - 1
        bins = torch.clamp(bins, 0, self.num_yield_bins - 1)
        return bins.view(-1)
    
    def log_cosh_loss(self, y_pred, y_true):
        """
        计算log(cosh(y_pred - y_true))损失函数
        
        参数：
        - y_pred: 预测值 [B, 1]
        - y_true: 真实值 [B, 1]
        
        返回：
        - loss: 损失值 [B, 1]
        """
        return torch.log(torch.cosh(y_pred - y_true))
    
    def huber_loss(self, y_pred, y_true, delta=1.0):
        """
        计算Huber损失，结合了MSE和MAE的优点
        
        参数：
        - y_pred: 预测值 [B, 1]
        - y_true: 真实值 [B, 1]
        - delta: Huber损失的阈值参数
        
        返回：
        - loss: 损失值 [B, 1]
        """
        abs_diff = torch.abs(y_pred - y_true)
        quadratic = torch.min(abs_diff, torch.tensor(delta, device=abs_diff.device))
        linear = abs_diff - quadratic
        return 0.5 * quadratic**2 + delta * linear
    
    def mse_loss(self, y_pred, y_true):
        """
        计算均方误差损失 (MSE)
        
        参数：
        - y_pred: 预测值 [B, 1]
        - y_true: 真实值 [B, 1]
        
        返回：
        - loss: 损失值 [B, 1]
        """
        return 0.5 * (y_pred - y_true)**2
    
    def mae_loss(self, y_pred, y_true):
        """
        计算平均绝对误差损失 (MAE)
        
        参数：
        - y_pred: 预测值 [B, 1]
        - y_true: 真实值 [B, 1]
        
        返回：
        - loss: 损失值 [B, 1]
        """
        return torch.abs(y_pred - y_true)
        
    def compute_regression_loss(self, predicted_yields, target_yields):
        """
        根据配置的损失函数类型计算回归损失
        
        参数：
        - predicted_yields: 预测产量 [B, 1]
        - target_yields: 目标产量 [B, 1]
        
        返回：
        - loss: 损失值
        """
        if self.loss_type == 'log_cosh':
            return self.log_cosh_loss(predicted_yields, target_yields).mean()
        elif self.loss_type == 'huber':
            return self.huber_loss(predicted_yields, target_yields).mean()
        elif self.loss_type == 'mse':
            return self.mse_loss(predicted_yields, target_yields).mean()
        elif self.loss_type == 'mae':
            return self.mae_loss(predicted_yields, target_yields).mean()
        else:
            # 默认使用log_cosh损失
            return self.log_cosh_loss(predicted_yields, target_yields).mean()
    
    def compute_classification_loss(self, logits, target_bins):
        """
        计算分类损失
        
        参数：
        - logits: 分类logits [B, num_yield_bins]
        - target_bins: 目标箱索引 [B]
        
        返回：
        - loss: 损失值
        """
        if self.loss_type == 'ce':
            # 标准交叉熵损失
            return F.cross_entropy(logits, target_bins)
        elif self.loss_type == 'focal':
            # 实现Focal Loss，对难分类样本给予更高权重
            gamma = 2.0  # Focal Loss的聚焦参数
            ce_loss = F.cross_entropy(logits, target_bins, reduction='none')
            p_t = torch.exp(-ce_loss)
            focal_loss = (1 - p_t) ** gamma * ce_loss
            return focal_loss.mean()
        elif self.loss_type == 'label_smoothing':
            # 标签平滑，提高泛化能力
            smoothing = 0.1
            return F.cross_entropy(logits, target_bins, label_smoothing=smoothing)
        else:
            # 默认使用交叉熵损失
            return F.cross_entropy(logits, target_bins)
    
    def info_nce_loss(self, features_1, features_2, batch_indices=None):
        """
        计算InfoNCE对比损失
        
        参数：
        - features_1: 第一组特征 [B, hidden_dim]
        - features_2: 第二组特征 [B, hidden_dim]
        - batch_indices: 批次中样本的索引，用于标识同一县和同一年份的样本
                        如果为None，则假设每个样本只与自己匹配
        
        返回：
        - loss: 对比损失值
        """
        batch_size = features_1.shape[0]
        device = features_1.device
        
        # 如果没有提供批次索引，则假设每个样本只与自己匹配
        if batch_indices is None:
            batch_indices = torch.arange(batch_size, device=device)
        
        # 计算所有样本对之间的相似度矩阵
        sim_matrix = torch.matmul(features_1, features_2.T) / self.temperature  # [B, B]
        
        # 使用向量化方式构造标签矩阵
        labels = (batch_indices.unsqueeze(0) == batch_indices.unsqueeze(1)).float()

        # 取出正样本对（保留每行中所有为1的元素）
        pos_sim = sim_matrix[labels.bool()].view(batch_size, -1)
        neg_sim = sim_matrix[~labels.bool()].view(batch_size, -1)
        
        # 将正负样本对拼接起来，每一行的第一个为正样本对
        logits = torch.cat([pos_sim, neg_sim], dim=1)
        
        # 标签：第一列为正样本
        labels = torch.zeros(batch_size, device=device, dtype=torch.long)
        
        loss = F.cross_entropy(logits, labels)
        
        return loss
    
    def compute_contrastive_loss(self, hist_features, climate_features, batch_indices=None):
        """
        计算直方图特征和气候特征之间的对比损失
        
        参数：
        - hist_features: 直方图特征 [B, hidden_dim]
        - climate_features: 气候特征 [B, hidden_dim]
        - batch_indices: 批次中样本的索引，用于标识同一县和同一年份的样本
        
        返回：
        - loss: 对比损失值
        """
        # 投影特征到对比学习空间
        hist_proj = F.normalize(hist_features, dim=1)
        climate_proj = F.normalize(climate_features, dim=1)
        
        # 计算双向对比损失
        loss_h2c = self.info_nce_loss(hist_proj, climate_proj, batch_indices)
        loss_c2h = self.info_nce_loss(climate_proj, hist_proj, batch_indices)
        
        return (loss_h2c + loss_c2h) / 2.0

    def get_expert_weights(self, histogram, climate):
        """
        获取每个样本的专家权重分布和对应的预测结果
        
        参数:
        - histogram: 直方图数据 [B, 20, 6, num_bins]
        - climate: 气候数据 [B, num_months, num_features]
        
        返回:
        - weights: 专家权重 [B, num_experts]
        - predictions: 预测产量 [B, 1]
        """
        # 获取直方图特征
        hist_features = self.histogram_encoder(histogram)  # [B, hidden_dim]
        
        # 获取门控网络的专家权重
        gate_weights, _ = self.climate_gate(climate)  # [B, num_experts]
        
        # 应用每个专家并收集输出
        expert_outputs = []
        for i, expert in enumerate(self.experts):
            expert_output = expert(hist_features)  # [B, 1] 或 [B, num_yield_bins]
            expert_outputs.append(expert_output)
        
        # 堆叠专家输出
        stacked_expert_outputs = torch.stack(expert_outputs, dim=1)  # [B, num_experts, 1] 或 [B, num_experts, num_yield_bins]
        
        # 根据模式计算预测值
        if self.mode == 'regression':
            gate_weights_expanded = gate_weights.unsqueeze(-1)  # [B, num_experts, 1]
            weighted_outputs = stacked_expert_outputs * gate_weights_expanded  # [B, num_experts, 1]
            predictions = weighted_outputs.sum(dim=1)  # [B, 1]
        else:  # classification
            # 计算logits
            gate_weights_expanded = gate_weights.unsqueeze(-1).expand(-1, -1, self.num_yield_bins)
            weighted_outputs = stacked_expert_outputs * gate_weights_expanded  # [B, num_experts, num_yield_bins]
            logits = weighted_outputs.sum(dim=1)  # [B, num_yield_bins]
            
            # 计算产量预测（期望值）
            probs = F.softmax(logits, dim=-1)  # [B, num_yield_bins]
            predictions = torch.sum(self.bin_centers.unsqueeze(0) * probs, dim=1, keepdim=True)  # [B, 1]
        
        return gate_weights, predictions
        
    def forward(self, histogram, climate, compute_loss=False, batch_indices=None, target_yields=None, use_contrastive=False):
        # histogram: [B, 20, 6, num_bins]
        # climate: [B, num_months, num_features]
        # batch_indices: 可选，用于标识同一县和同一年份的样本
        # compute_loss: 是否计算损失
        # target_yields: 目标产量值，用于计算损失函数

        # 获取直方图特征
        hist_features = self.histogram_encoder(histogram)  # [B, hidden_dim]
        
        # 获取门控网络的专家权重和气候特征
        gate_weights, climate_features = self.climate_gate(climate)  # 触发前向传播以更新mid_representation
        
        # 对比损失相关计算
        contrastive_loss = 0.0
        if compute_loss and (use_contrastive or self.pretrain_mode):
            contrastive_loss = self.compute_contrastive_loss(hist_features, climate_features, batch_indices)
        
        # 如果是预训练模式，只返回对比损失
        if self.pretrain_mode:
            return None, contrastive_loss

        # 应用每个专家并收集输出
        expert_outputs = []
        for i, expert in enumerate(self.experts):
            expert_output = expert(hist_features)  # [B, 1] 或 [B, num_yield_bins]
            expert_outputs.append(expert_output)

        # 堆叠专家输出
        stacked_expert_outputs = torch.stack(expert_outputs, dim=1)  # [B, num_experts, 1] 或 [B, num_experts, num_yield_bins]

        # 应用门控权重
        if self.mode == 'regression':
            gate_weights = gate_weights.unsqueeze(-1)  # [B, num_experts, 1]
            weighted_outputs = stacked_expert_outputs * gate_weights  # [B, num_experts, 1]
            outputs = weighted_outputs.sum(dim=1)  # [B, 1]
            
            # 计算回归损失
            reg_loss = 0.0
            if compute_loss and target_yields is not None:
                reg_loss = self.compute_regression_loss(outputs, target_yields)
                
            if compute_loss:
                return outputs, contrastive_loss, reg_loss
            
            return outputs
            
        else:  # classification
            # 将[B, num_experts]扩展到[B, num_experts, num_yield_bins]
            gate_weights = gate_weights.unsqueeze(-1).expand(-1, -1, self.num_yield_bins)
            weighted_outputs = stacked_expert_outputs * gate_weights  # [B, num_experts, num_yield_bins]
            logits = weighted_outputs.sum(dim=1)  # [B, num_yield_bins]
            
            # 计算分类损失
            cls_loss = 0.0
            if compute_loss and target_yields is not None:
                target_bins = self.get_yield_bin(target_yields)
                cls_loss = self.compute_classification_loss(logits, target_bins)
            
            # 如果只需要logits，直接返回
            if not compute_loss or target_yields is None:
                return logits
                
            # 计算产量预测（期望值）用于评估
            probs = F.softmax(logits, dim=-1)  # [B, num_yield_bins]
            predicted_yields = torch.sum(self.bin_centers.unsqueeze(0) * probs, dim=1, keepdim=True)  # [B, 1]
            
            if compute_loss:
                return predicted_yields, contrastive_loss, cls_loss
            
            return predicted_yields
