import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from data.combined_data import CombinedDataset, get_data_loader
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, r2_score
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class HistogramLSTM(nn.Module):
    def __init__(self, input_size, hidden_size=128, num_layers=2, dropout=0.2):
        """
        基于直方图数据的LSTM模型
        
        Args:
            input_size: 输入特征维度 (6 * num_bins)
            hidden_size: LSTM隐藏层大小
            num_layers: LSTM层数
            dropout: dropout比率
        """
        super(HistogramLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 全连接层
        self.fc = nn.Sequential(
            nn.Linear(hidden_size, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 1)
        )
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量，形状为 (batch_size, seq_len, input_size)
            
        Returns:
            预测值，形状为 (batch_size, 1)
        """
        # LSTM前向传播
        lstm_out, _ = self.lstm(x)
        # 只使用最后一个时间步的输出
        last_hidden = lstm_out[:, -1, :]
        # 通过全连接层
        out = self.fc(last_hidden)
        return out

def train_model(model, train_loader, val_loader, criterion, optimizer, num_epochs, device):
    """
    训练模型
    
    Args:
        model: LSTM模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        criterion: 损失函数
        optimizer: 优化器
        num_epochs: 训练轮数
        device: 计算设备
    """
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        for batch_idx, (histograms, _, yield_values) in enumerate(train_loader):
            # 重塑直方图数据以适应LSTM输入
            batch_size = histograms.size(0)
            histograms = histograms.reshape(batch_size, 20, -1)  # (batch_size, 20, 6*num_bins)
            
            # 将数据移到设备上
            histograms = histograms.to(device)
            yield_values = yield_values.to(device)
            
            # 前向传播
            outputs = model(histograms)
            loss = criterion(outputs, yield_values)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            
            if batch_idx % 10 == 0:
                logging.info(f'Epoch [{epoch+1}/{num_epochs}], Batch [{batch_idx}/{len(train_loader)}], '
                           f'Loss: {loss.item():.4f}')
        
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_predictions = []
        val_targets = []
        
        with torch.no_grad():
            for histograms, _, yield_values in val_loader:
                batch_size = histograms.size(0)
                histograms = histograms.reshape(batch_size, 20, -1)
                histograms = histograms.to(device)
                yield_values = yield_values.to(device)
                
                outputs = model(histograms)
                loss = criterion(outputs, yield_values)
                val_loss += loss.item()
                
                val_predictions.extend(outputs.cpu().numpy())
                val_targets.extend(yield_values.cpu().numpy())
        
        avg_val_loss = val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        
        # 计算验证集上的R2分数
        val_r2 = r2_score(val_targets, val_predictions)
        
        logging.info(f'Epoch [{epoch+1}/{num_epochs}], '
                    f'Train Loss: {avg_train_loss:.4f}, '
                    f'Val Loss: {avg_val_loss:.4f}, '
                    f'Val R2: {val_r2:.4f}')
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_model.pth')
    
    return train_losses, val_losses

def plot_losses(train_losses, val_losses):
    """绘制训练和验证损失曲线"""
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Training Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.title('Training and Validation Losses')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.savefig('loss_curves.png')
    plt.close()

def main():
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f'Using device: {device}')
    
    # 数据参数
    base_dir = "datasets"
    train_years = list(range(2017, 2022))
    val_years = [2022]
    num_bins = 64
    batch_size = 32
    
    # 创建数据加载器
    train_loader = get_data_loader(
        base_dir=base_dir,
        years=train_years,
        batch_size=batch_size,
        num_bins=num_bins,
        histogram_norm='std'
    )
    
    val_loader = get_data_loader(
        base_dir=base_dir,
        years=val_years,
        batch_size=batch_size,
        num_bins=num_bins,
        histogram_norm='std'
    )
    
    # 模型参数
    input_size = 6 * num_bins  # 6个特征，每个特征有num_bins个bin
    hidden_size = 128
    num_layers = 2
    dropout = 0.2
    
    # 创建模型
    model = HistogramLSTM(
        input_size=input_size,
        hidden_size=hidden_size,
        num_layers=num_layers,
        dropout=dropout
    ).to(device)
    
    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    # 训练模型
    num_epochs = 50
    train_losses, val_losses = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        criterion=criterion,
        optimizer=optimizer,
        num_epochs=num_epochs,
        device=device
    )
    
    # 绘制损失曲线
    plot_losses(train_losses, val_losses)
    
    # 加载最佳模型进行最终评估
    model.load_state_dict(torch.load('best_model.pth'))
    model.eval()
    
    # 在验证集上进行最终评估
    val_predictions = []
    val_targets = []
    
    with torch.no_grad():
        for histograms, _, yield_values in val_loader:
            batch_size = histograms.size(0)
            histograms = histograms.reshape(batch_size, 20, -1)
            histograms = histograms.to(device)
            
            outputs = model(histograms)
            val_predictions.extend(outputs.cpu().numpy())
            val_targets.extend(yield_values.numpy())
    
    # 计算最终评估指标
    mse = mean_squared_error(val_targets, val_predictions)
    rmse = np.sqrt(mse)
    r2 = r2_score(val_targets, val_predictions)
    
    logging.info(f'Final Evaluation Metrics:')
    logging.info(f'MSE: {mse:.4f}')
    logging.info(f'RMSE: {rmse:.4f}')
    logging.info(f'R2 Score: {r2:.4f}')

if __name__ == '__main__':
    main()
